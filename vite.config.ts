import { defineConfig } from "vite";
import vue from "@vitejs/plugin-vue";
import UnoCSS from "unocss/vite";

// https://vite.dev/config/
export default defineConfig({
  plugins: [vue(), UnoCSS()],
  resolve: {
    alias: {
      "@": "/src",
    },
  },
  assetsInclude: ["**/*.rfa", "**/*.rvt", "**/*.fbx"], // 包含 RFA、RVT 和 FBX 文件作为静态资源
  server: {
    fs: {
      allow: [".."], // 允许访问上级目录
    },
  },
});
