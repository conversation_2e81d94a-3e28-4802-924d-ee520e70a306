<template>
  <div class="alarm-table-container h-full flex flex-col">
    <!-- 表格头部 -->
    <div class="table-header border-b border-#0efcff/30">
      <div class="table-row px-4 py-3">
        <div class="table-grid">
          <div class="col-pool font-bold">水池名称</div>
          <div class="col-device font-bold">设备名称</div>
          <div class="col-reason font-bold">报警原因</div>
          <div class="col-time font-bold">报警时间</div>
        </div>
      </div>
    </div>

    <!-- 表格内容 -->
    <div class="table-body flex-1 overflow-hidden">
      <div
        class="table-content"
        :style="{ transform: `translateY(-${currentOffset}px)` }"
      >
        <div
          v-for="(item, index) in displayData"
          :key="`${index}-${item.poolName}-${item.time}`"
          class="table-row border-b border-#0efcff/10 px-4 py-3 transition-colors hover:bg-#0efcff/5"
          :class="{ 'bg-#0efcff/3': index % 2 === 0 }"
        >
          <div class="table-grid">
            <div class="col-pool truncate">
              {{ item.poolName }}
            </div>
            <div class="col-device">{{ item.deviceName }}</div>
            <div class="col-reason alarm-reason-text font-bold">
              {{ item.reason }}
            </div>
            <div class="col-time text-#0efcff">
              {{ item.time }}
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, computed } from "vue";

interface AlarmItem {
  poolName: string;
  deviceName: string;
  reason: string;
  time: string;
  level: "high" | "medium" | "low"; // 报警级别
}

// 模拟报警数据
const alarmData = ref<AlarmItem[]>([
  {
    poolName: "1号池",
    deviceName: "温度传感器",
    reason: "水温过高",
    time: "2025-07-23 14:30",
    level: "high",
  },
  {
    poolName: "3号池",
    deviceName: "pH传感器",
    reason: "pH值异常",
    time: "2025-07-23 14:25",
    level: "medium",
  },
  {
    poolName: "7号池",
    deviceName: "溶氧仪",
    reason: "溶氧不足",
    time: "2025-07-23 14:20",
    level: "high",
  },
  {
    poolName: "12号池",
    deviceName: "水位传感器",
    reason: "水位过低",
    time: "2025-07-23 14:15",
    level: "medium",
  },
  {
    poolName: "15号池",
    deviceName: "盐度计",
    reason: "盐度超标",
    time: "2025-07-23 14:10",
    level: "low",
  },
  {
    poolName: "5号池",
    deviceName: "流量计",
    reason: "流量异常",
    time: "2025-07-23 14:05",
    level: "medium",
  },
  {
    poolName: "9号池",
    deviceName: "浊度仪",
    reason: "水质浑浊",
    time: "2025-07-23 14:00",
    level: "low",
  },
  {
    poolName: "18号池",
    deviceName: "氨氮检测仪",
    reason: "氨氮超标",
    time: "2025-07-23 13:55",
    level: "high",
  },
]);

const currentOffset = ref(0);
const rowHeight = 52; // 每行的高度（包括padding和border）
const containerHeight = ref(0);
const visibleRows = ref(0);
const scrollSpeed = ref(0.5); // 每次滚动的像素数，控制滚动速度
let animationId: number | null = null;

// 计算可见行数
const calculateVisibleRows = () => {
  const container = document.querySelector(".table-body");
  if (container) {
    containerHeight.value = container.clientHeight;
    visibleRows.value = Math.floor(containerHeight.value / rowHeight);
  }
};

// 创建循环数据用于无缝滚动
const displayData = computed(() => {
  // 复制数据以实现无缝循环
  return [...alarmData.value, ...alarmData.value];
});

// 自动滚动函数
const autoScroll = () => {
  if (visibleRows.value === 0) {
    animationId = requestAnimationFrame(autoScroll);
    return;
  }

  // 计算一个完整数据集的高度
  const singleSetHeight = alarmData.value.length * rowHeight;

  // 平滑滚动
  currentOffset.value += scrollSpeed.value;

  // 当滚动超过一个完整数据集时，重置到开始位置
  if (currentOffset.value >= singleSetHeight) {
    currentOffset.value = 0;
  }

  // 继续下一帧动画
  animationId = requestAnimationFrame(autoScroll);
};

onMounted(() => {
  // 延迟计算，确保DOM已渲染
  setTimeout(() => {
    calculateVisibleRows();
    // 开始自动滚动动画
    animationId = requestAnimationFrame(autoScroll);
  }, 100);

  // 监听窗口大小变化
  window.addEventListener("resize", calculateVisibleRows);
});

onUnmounted(() => {
  if (animationId) {
    cancelAnimationFrame(animationId);
  }
  window.removeEventListener("resize", calculateVisibleRows);
});
</script>

<style scoped>
.alarm-table-container {
  font-size: 13px;
  color: #0efcff;
}

.table-row {
  min-height: 52px;
  display: flex;
  align-items: center;
}

.table-header {
  background: rgba(14, 252, 255, 0.15);
  backdrop-filter: blur(5px);
  border-radius: 4px 4px 0 0;
  text-shadow: 0 0 8px rgba(14, 252, 255, 0.6);
}

.table-header .table-row {
  background: transparent;
}

.table-row:hover {
  background: rgba(14, 252, 255, 0.08) !important;
  box-shadow: inset 0 0 20px rgba(14, 252, 255, 0.1);
}

/* 表格网格布局 */
.table-grid {
  display: grid;
  grid-template-columns: 1fr 1fr 1.5fr 1fr;
  gap: 1rem;
  align-items: center;
  width: 100%;
}

.col-pool,
.col-device,
.col-reason,
.col-time {
  text-align: center;
  justify-self: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 发光效果 */
.table-header div {
  text-shadow: 0 0 12px rgba(102, 204, 255, 0.9);
}

/* 报警原因高亮 */
.alarm-reason-text {
  color: #f48fb1 !important;
  text-shadow: 0 0 10px rgba(244, 143, 177, 0.7);
  animation: alarmBlink 2s infinite;
}

@keyframes alarmBlink {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

/* 时间文字发光 */
.text-#0efcff {
  text-shadow: 0 0 10px rgba(102, 204, 255, 0.7);
}
</style>
