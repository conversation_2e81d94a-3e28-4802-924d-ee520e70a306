<template>
  <div class="h-0 w-full flex-1 border" ref="container"></div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from "vue";
import * as THREE from "three";
import { FBXLoader } from "three/examples/jsm/loaders/FBXLoader.js";

// 获取容器引用
const container = ref<HTMLDivElement>();

// Three.js 相关变量
let scene: THREE.Scene;
let camera: THREE.PerspectiveCamera;
let renderer: THREE.WebGLRenderer;
let poolModel: THREE.Group | null = null;
let animationId: number;

// 初始化 Three.js
function initThreeJS() {
  if (!container.value) return;

  // 获取容器尺寸
  const width = container.value.clientWidth;
  const height = container.value.clientHeight;

  // 创建场景
  scene = new THREE.Scene();

  // 创建相机
  camera = new THREE.PerspectiveCamera(
    45, // 视角
    width / height, // 宽高比
    0.1, // 近平面
    1000 // 远平面
  );

  // 创建渲染器
  renderer = new THREE.WebGLRenderer({ antialias: true });
  renderer.setSize(width, height);
  renderer.setPixelRatio(window.devicePixelRatio);

  // 将渲染器添加到容器中
  container.value.appendChild(renderer.domElement);

  // 添加光源
  const ambientLight = new THREE.AmbientLight(0x404040, 0.6);
  scene.add(ambientLight);

  const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
  directionalLight.position.set(10, 10, 5);
  scene.add(directionalLight);

  // 加载FBX模型
  loadPoolModel();

  // 设置相机位置
  camera.position.set(0, 5, 10);
  camera.lookAt(0, 0, 0);

  // 开始动画
  animate();
}

// 加载FBX模型
function loadPoolModel() {
  const loader = new FBXLoader();

  // 尝试多个路径
  const modelPaths = [
    "/models/pool.fbx",
    "/src/assets/pool.fbx",
    "../assets/pool.fbx",
  ];

  let currentPathIndex = 0;

  function tryLoadModel() {
    if (currentPathIndex >= modelPaths.length) {
      console.error("Failed to load model from all paths");
      // 创建一个简单的替代几何体
      createFallbackGeometry();
      return;
    }

    const currentPath = modelPaths[currentPathIndex];
    console.log(`Trying to load model from: ${currentPath}`);

    loader.load(
      currentPath,
      (object) => {
        poolModel = object;

        // 调整模型大小和位置
        object.scale.setScalar(0.01); // 根据需要调整缩放
        object.position.set(0, 0, 0);

        // 添加到场景
        scene.add(object);

        console.log(`Pool model loaded successfully from: ${currentPath}`);
      },
      (progress) => {
        console.log(
          `Loading progress from ${currentPath}:`,
          (progress.loaded / progress.total) * 100 + "%"
        );
      },
      (error) => {
        console.error(`Error loading from ${currentPath}:`, error);
        currentPathIndex++;
        tryLoadModel(); // 尝试下一个路径
      }
    );
  }

  tryLoadModel();
}

// 创建备用几何体（如果FBX加载失败）
function createFallbackGeometry() {
  console.log("Creating fallback geometry...");

  // 创建一个简单的池子形状
  const poolGeometry = new THREE.CylinderGeometry(2, 2, 0.5, 32);
  const poolMaterial = new THREE.MeshLambertMaterial({
    color: 0x4a90e2,
    transparent: true,
    opacity: 0.8,
  });

  const pool = new THREE.Mesh(poolGeometry, poolMaterial);
  pool.position.set(0, 0, 0);

  poolModel = new THREE.Group();
  poolModel.add(pool);

  scene.add(poolModel);

  console.log("Fallback pool geometry created");
}

// 渲染函数
function animate() {
  animationId = requestAnimationFrame(animate);

  // 如果模型已加载，可以添加旋转动画
  if (poolModel) {
    poolModel.rotation.y += 0.005; // 缓慢旋转
  }

  // 渲染
  renderer.render(scene, camera);
}

// 处理窗口大小变化
function handleResize() {
  if (!container.value || !camera || !renderer) return;

  const width = container.value.clientWidth;
  const height = container.value.clientHeight;

  camera.aspect = width / height;
  camera.updateProjectionMatrix();
  renderer.setSize(width, height);
}

// 清理资源
function cleanup() {
  if (animationId) {
    cancelAnimationFrame(animationId);
  }

  // 清理模型
  if (poolModel) {
    scene.remove(poolModel);
    poolModel = null;
  }

  if (renderer) {
    renderer.dispose();
  }

  window.removeEventListener("resize", handleResize);
}

// 组件挂载时初始化
onMounted(() => {
  initThreeJS();
  window.addEventListener("resize", handleResize);
});

// 组件卸载时清理
onUnmounted(() => {
  cleanup();
});
</script>
